#!/usr/bin/env python3
"""
调试API密钥读取问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

print("🔍 调试API密钥读取问题")
print("=" * 50)

# 1. 检查环境变量（不加载.env）
print("1️⃣ 检查系统环境变量中的QINGYUN_API_KEY:")
system_key = os.getenv("QINGYUN_API_KEY")
if system_key:
    print(f"   系统环境变量: {system_key[:20]}...")
else:
    print("   系统环境变量: 未设置")

# 2. 加载.env文件后检查
print("\n2️⃣ 加载.env文件后检查:")
from dotenv import load_dotenv
load_dotenv()

dotenv_key = os.getenv("QINGYUN_API_KEY")
if dotenv_key:
    print(f"   .env文件加载后: {dotenv_key[:20]}...")
else:
    print("   .env文件加载后: 未设置")

# 3. 直接读取.env文件内容
print("\n3️⃣ 直接读取.env文件内容:")
env_file = Path(".env")
if env_file.exists():
    with open(env_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    qingyun_lines = [line.strip() for line in lines if 'QINGYUN_API_KEY' in line and not line.strip().startswith('#')]
    print(f"   找到 {len(qingyun_lines)} 行QINGYUN_API_KEY配置:")
    for i, line in enumerate(qingyun_lines, 1):
        if '=' in line:
            key_value = line.split('=', 1)[1]
            print(f"   {i}. {line.split('=', 1)[0]}={key_value[:20]}...")
        else:
            print(f"   {i}. {line}")
else:
    print("   .env文件不存在")

# 4. 测试通过models.py获取的密钥
print("\n4️⃣ 测试通过models.py获取的密钥:")
try:
    from src.llm.models import get_model, ModelProvider
    
    # 这会触发API密钥读取
    try:
        llm = get_model("meta-llama/llama-4-scout", ModelProvider.QINGYUN)
        print("   ✅ 成功创建QingYun模型实例")
        
        # 检查实际使用的API密钥
        if hasattr(llm, 'openai_api_key'):
            actual_key = str(llm.openai_api_key)
            if actual_key and actual_key != 'None':
                print(f"   实际使用的密钥: {actual_key[:20]}...")
            else:
                print("   实际使用的密钥: 未获取到")
        else:
            print("   无法获取实际使用的密钥")
            
    except Exception as e:
        print(f"   ❌ 创建模型失败: {e}")
        
except Exception as e:
    print(f"   ❌ 导入失败: {e}")

# 5. 检查是否有多个.env文件
print("\n5️⃣ 检查是否有多个.env文件:")
possible_env_files = [
    ".env",
    ".env.local", 
    ".env.development",
    ".env.production",
    "src/.env",
    "tradingagents/.env"
]

for env_path in possible_env_files:
    if Path(env_path).exists():
        print(f"   ✅ 找到: {env_path}")
    else:
        print(f"   ❌ 不存在: {env_path}")

print("\n" + "=" * 50)
print("🎯 结论:")
if dotenv_key:
    print(f"✅ 当前应该使用的API密钥: {dotenv_key[:20]}...")
    if dotenv_key.startswith("sk-OdvSzVvK37ODtq2p5nJllTE4m552pg45coJJVoL3CiIgiZtD"):
        print("✅ 这是您期望的新密钥")
    else:
        print("❌ 这不是您期望的新密钥")
else:
    print("❌ 无法读取到API密钥")
